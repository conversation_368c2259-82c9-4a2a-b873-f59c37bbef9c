.field-content {
  padding-left: 20px;
}

.card-shadow {
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 8px;
}

.card-shadow-white {
    background-color: white;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.layout-container {
 display: flex;
 flex-direction: column;
 gap: 8px;
}

.disabled input {
  cursor: not-allowed;
  background-color: #F0F0F0 !important;
  color: #626367;
}

.field-object,
.field-string,
.field-boolean,
.field-number,
.field-array {
  margin-bottom: 0px !important;
}

.rjsf > .form-group {
  margin-bottom: 0px !important;
}