import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Checkbox, FormControlLabel } from '@mui/material';
import { CheckedIcon, UnCheckedIcon } from '../../../Icons';

const CustomCheckboxWidget: React.FC<WidgetProps> = ({
  id,
  value,
  disabled,
  readonly,
  onChange,
  onBlur,
  onFocus,
  label,
  schema,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.checked);
  };

  const handleBlur = () => {
    onBlur && onBlur(id, value);
  };

  const handleFocus = () => {
    onFocus && onFocus(id, value);
  };

  const labelText = label || schema?.title || '';

  return (
    <FormControlLabel
      control={
        <Checkbox
          id={id}
          checked={value || false}
          disabled={disabled || readonly}
          onChange={handleChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          checkedIcon={<CheckedIcon />}
          icon={<UnCheckedIcon />}
          sx={{
            padding: '9px',
            '&:hover': {
              backgroundColor: 'rgba(39, 114, 91, 0.04)',
            },
          }}
        />
      }
      label={labelText}
      sx={{
        margin: 0,
        alignItems: 'center',
        '& .MuiFormControlLabel-label': {
          fontSize: '16px',
          fontWeight: 400,
          fontFamily: 'Roboto',
          color: '#222229',
        },
      }}
    />
  );
};

export default CustomCheckboxWidget;
