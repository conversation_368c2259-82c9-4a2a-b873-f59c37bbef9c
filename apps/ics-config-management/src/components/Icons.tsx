import { SvgIcon, SvgIconProps } from '@mui/material';
import React from 'react';

const svgToIcon = (path, viewBox = '0 0 16 16') =>
  React.forwardRef<SVGSVGElement, SvgIconProps>((props, ref) => (
    <SvgIcon {...props} ref={ref} viewBox={viewBox}>
      {path}
    </SvgIcon>
  ));

export const TenantIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M13.3333 4.66667H10.6666C10.6666 3.19333 9.47325 2 7.99992 2C6.52659 2 5.33325 3.19333 5.33325 4.66667H2.66659C1.93325 4.66667 1.33325 5.26667 1.33325 6V8C1.33325 8.74 1.92659 9.33333 2.66659 9.33333H6.66659V8.66667C6.66659 8.3 6.96659 8 7.33325 8H8.66659C9.03325 8 9.33325 8.3 9.33325 8.66667V9.33333H13.3333C14.0666 9.33333 14.6666 8.73333 14.6666 8V6C14.6666 5.26667 14.0666 4.66667 13.3333 4.66667ZM7.33325 10.6667C6.96659 10.6667 6.66659 10.3667 6.66659 10H2.00659V12.6667C2.00659 13.4 2.60659 14 3.33992 14H12.6666C13.3999 14 13.9999 13.4 13.9999 12.6667V10H9.33325C9.33325 10.3667 9.03325 10.6667 8.66659 10.6667H7.33325ZM7.99992 3.33333C7.26658 3.33333 6.66658 3.93333 6.66658 4.66667H9.33325C9.33325 3.93333 8.73325 3.33333 7.99992 3.33333Z'
    fill='#4958A9'
  />
);

export const TenantIconWhite = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M13.3333 4.66667H10.6666C10.6666 3.19333 9.47325 2 7.99992 2C6.52659 2 5.33325 3.19333 5.33325 4.66667H2.66659C1.93325 4.66667 1.33325 5.26667 1.33325 6V8C1.33325 8.74 1.92659 9.33333 2.66659 9.33333H6.66659V8.66667C6.66659 8.3 6.96659 8 7.33325 8H8.66659C9.03325 8 9.33325 8.3 9.33325 8.66667V9.33333H13.3333C14.0666 9.33333 14.6666 8.73333 14.6666 8V6C14.6666 5.26667 14.0666 4.66667 13.3333 4.66667ZM7.33325 10.6667C6.96659 10.6667 6.66659 10.3667 6.66659 10H2.00659V12.6667C2.00659 13.4 2.60659 14 3.33992 14H12.6666C13.3999 14 13.9999 13.4 13.9999 12.6667V10H9.33325C9.33325 10.3667 9.03325 10.6667 8.66659 10.6667H7.33325ZM7.99992 3.33333C7.26658 3.33333 6.66658 3.93333 6.66658 4.66667H9.33325C9.33325 3.93333 8.73325 3.33333 7.99992 3.33333Z'
    fill='#FFFFFF'
  />
);

export const SiteIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M3.33325 6.17668C3.33325 3.59668 5.41992 1.51001 7.99992 1.51001C10.5799 1.51001 12.6666 3.59668 12.6666 6.17668C12.6666 8.95668 9.71992 12.79 8.51325 14.25C8.24659 14.57 7.75992 14.57 7.49325 14.25C6.27992 12.79 3.33325 8.95668 3.33325 6.17668ZM6.33325 6.17668C6.33325 7.09668 7.07992 7.84334 7.99992 7.84334C8.91992 7.84334 9.66659 7.09668 9.66659 6.17668C9.66659 5.25668 8.91992 4.51001 7.99992 4.51001C7.07992 4.51001 6.33325 5.25668 6.33325 6.17668Z'
    fill='#4958A9'
  />
);

export const SiteIconWhite = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M3.33325 6.17668C3.33325 3.59668 5.41992 1.51001 7.99992 1.51001C10.5799 1.51001 12.6666 3.59668 12.6666 6.17668C12.6666 8.95668 9.71992 12.79 8.51325 14.25C8.24659 14.57 7.75992 14.57 7.49325 14.25C6.27992 12.79 3.33325 8.95668 3.33325 6.17668ZM6.33325 6.17668C6.33325 7.09668 7.07992 7.84334 7.99992 7.84334C8.91992 7.84334 9.66659 7.09668 9.66659 6.17668C9.66659 5.25668 8.91992 4.51001 7.99992 4.51001C7.07992 4.51001 6.33325 5.25668 6.33325 6.17668Z'
    fill='#FFFFFF'
  />
);

export const SiteTagIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M8.27325 1.71992L14.2733 7.71992C14.5133 7.95992 14.6666 8.29992 14.6666 8.66659C14.6666 9.03325 14.5199 9.36659 14.2733 9.60659L9.60659 14.2733C9.36659 14.5199 9.03325 14.6666 8.66659 14.6666C8.29992 14.6666 7.96659 14.5199 7.72659 14.2799L1.72659 8.27992C1.47992 8.03325 1.33325 7.69992 1.33325 7.33325V2.66659C1.33325 1.93325 1.93325 1.33325 2.66659 1.33325H7.33325C7.69992 1.33325 8.03325 1.47992 8.27325 1.71992ZM2.66659 3.66659C2.66659 4.21992 3.11325 4.66658 3.66659 4.66658C4.21992 4.66658 4.66658 4.21992 4.66658 3.66659C4.66658 3.11325 4.21992 2.66659 3.66659 2.66659C3.11325 2.66659 2.66659 3.11325 2.66659 3.66659Z'
    fill='#4958A9'
  />
);

export const SiteTagIconWhite = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M8.27325 1.71992L14.2733 7.71992C14.5133 7.95992 14.6666 8.29992 14.6666 8.66659C14.6666 9.03325 14.5199 9.36659 14.2733 9.60659L9.60659 14.2733C9.36659 14.5199 9.03325 14.6666 8.66659 14.6666C8.29992 14.6666 7.96659 14.5199 7.72659 14.2799L1.72659 8.27992C1.47992 8.03325 1.33325 7.69992 1.33325 7.33325V2.66659C1.33325 1.93325 1.93325 1.33325 2.66659 1.33325H7.33325C7.69992 1.33325 8.03325 1.47992 8.27325 1.71992ZM2.66659 3.66659C2.66659 4.21992 3.11325 4.66658 3.66659 4.66658C4.21992 4.66658 4.66658 4.21992 4.66658 3.66659C4.66658 3.11325 4.21992 2.66659 3.66659 2.66659C3.11325 2.66659 2.66659 3.11325 2.66659 3.66659Z'
    fill='#FFFFFF'
  />
);

export const DeviceIcon = svgToIcon(
  <path
    d='M11.3333 1.3335H4.66659C3.93325 1.3335 3.33325 1.9335 3.33325 2.66683V4.00016C3.33325 4.7335 3.93325 5.3335 4.66659 5.3335H11.3333C12.0666 5.3335 12.6666 4.7335 12.6666 4.00016V2.66683C12.6666 1.9335 12.0666 1.3335 11.3333 1.3335ZM10.9999 4.00016H4.99992C4.81325 4.00016 4.66659 3.8535 4.66659 3.66683V3.00016C4.66659 2.8135 4.81325 2.66683 4.99992 2.66683H10.9999C11.1866 2.66683 11.3333 2.8135 11.3333 3.00016V3.66683C11.3333 3.8535 11.1866 4.00016 10.9999 4.00016ZM13.3333 14.6668H2.66659C1.93325 14.6668 1.33325 14.0668 1.33325 13.3335V12.6668H14.6666V13.3335C14.6666 14.0668 14.0666 14.6668 13.3333 14.6668ZM12.3533 6.7935C12.1399 6.3135 11.6599 6.00016 11.1333 6.00016H4.86659C4.33992 6.00016 3.85992 6.3135 3.64659 6.7935L1.33325 12.0002H14.6666L12.3533 6.7935ZM6.33325 10.6668H5.66659C5.47992 10.6668 5.33325 10.5202 5.33325 10.3335C5.33325 10.1468 5.47992 10.0002 5.66659 10.0002H6.33325C6.51992 10.0002 6.66659 10.1468 6.66659 10.3335C6.66659 10.5202 6.51992 10.6668 6.33325 10.6668ZM6.33325 9.3335H5.66659C5.47992 9.3335 5.33325 9.18683 5.33325 9.00016C5.33325 8.8135 5.47992 8.66683 5.66659 8.66683H6.33325C6.51992 8.66683 6.66659 8.8135 6.66659 9.00016C6.66659 9.18683 6.51992 9.3335 6.33325 9.3335ZM6.33325 8.00016H5.66659C5.47992 8.00016 5.33325 7.8535 5.33325 7.66683C5.33325 7.48016 5.47992 7.3335 5.66659 7.3335H6.33325C6.51992 7.3335 6.66659 7.48016 6.66659 7.66683C6.66659 7.8535 6.51992 8.00016 6.33325 8.00016ZM8.33325 10.6668H7.66659C7.47992 10.6668 7.33325 10.5202 7.33325 10.3335C7.33325 10.1468 7.47992 10.0002 7.66659 10.0002H8.33325C8.51992 10.0002 8.66659 10.1468 8.66659 10.3335C8.66659 10.5202 8.51992 10.6668 8.33325 10.6668ZM8.33325 9.3335H7.66659C7.47992 9.3335 7.33325 9.18683 7.33325 9.00016C7.33325 8.8135 7.47992 8.66683 7.66659 8.66683H8.33325C8.51992 8.66683 8.66659 8.8135 8.66659 9.00016C8.66659 9.18683 8.51992 9.3335 8.33325 9.3335ZM8.33325 8.00016H7.66659C7.47992 8.00016 7.33325 7.8535 7.33325 7.66683C7.33325 7.48016 7.47992 7.3335 7.66659 7.3335H8.33325C8.51992 7.3335 8.66659 7.48016 8.66659 7.66683C8.66659 7.8535 8.51992 8.00016 8.33325 8.00016ZM10.3333 10.6668H9.66659C9.47992 10.6668 9.33325 10.5202 9.33325 10.3335C9.33325 10.1468 9.47992 10.0002 9.66659 10.0002H10.3333C10.5199 10.0002 10.6666 10.1468 10.6666 10.3335C10.6666 10.5202 10.5199 10.6668 10.3333 10.6668ZM10.3333 9.3335H9.66659C9.47992 9.3335 9.33325 9.18683 9.33325 9.00016C9.33325 8.8135 9.47992 8.66683 9.66659 8.66683H10.3333C10.5199 8.66683 10.6666 8.8135 10.6666 9.00016C10.6666 9.18683 10.5199 9.3335 10.3333 9.3335ZM10.3333 8.00016H9.66659C9.47992 8.00016 9.33325 7.8535 9.33325 7.66683C9.33325 7.48016 9.47992 7.3335 9.66659 7.3335H10.3333C10.5199 7.3335 10.6666 7.48016 10.6666 7.66683C10.6666 7.8535 10.5199 8.00016 10.3333 8.00016Z'
    fill='#4958A9'
  />
);

export const DeviceIconWhite = svgToIcon(
  <path
    d='M11.3333 1.3335H4.66659C3.93325 1.3335 3.33325 1.9335 3.33325 2.66683V4.00016C3.33325 4.7335 3.93325 5.3335 4.66659 5.3335H11.3333C12.0666 5.3335 12.6666 4.7335 12.6666 4.00016V2.66683C12.6666 1.9335 12.0666 1.3335 11.3333 1.3335ZM10.9999 4.00016H4.99992C4.81325 4.00016 4.66659 3.8535 4.66659 3.66683V3.00016C4.66659 2.8135 4.81325 2.66683 4.99992 2.66683H10.9999C11.1866 2.66683 11.3333 2.8135 11.3333 3.00016V3.66683C11.3333 3.8535 11.1866 4.00016 10.9999 4.00016ZM13.3333 14.6668H2.66659C1.93325 14.6668 1.33325 14.0668 1.33325 13.3335V12.6668H14.6666V13.3335C14.6666 14.0668 14.0666 14.6668 13.3333 14.6668ZM12.3533 6.7935C12.1399 6.3135 11.6599 6.00016 11.1333 6.00016H4.86659C4.33992 6.00016 3.85992 6.3135 3.64659 6.7935L1.33325 12.0002H14.6666L12.3533 6.7935ZM6.33325 10.6668H5.66659C5.47992 10.6668 5.33325 10.5202 5.33325 10.3335C5.33325 10.1468 5.47992 10.0002 5.66659 10.0002H6.33325C6.51992 10.0002 6.66659 10.1468 6.66659 10.3335C6.66659 10.5202 6.51992 10.6668 6.33325 10.6668ZM6.33325 9.3335H5.66659C5.47992 9.3335 5.33325 9.18683 5.33325 9.00016C5.33325 8.8135 5.47992 8.66683 5.66659 8.66683H6.33325C6.51992 8.66683 6.66659 8.8135 6.66659 9.00016C6.66659 9.18683 6.51992 9.3335 6.33325 9.3335ZM6.33325 8.00016H5.66659C5.47992 8.00016 5.33325 7.8535 5.33325 7.66683C5.33325 7.48016 5.47992 7.3335 5.66659 7.3335H6.33325C6.51992 7.3335 6.66659 7.48016 6.66659 7.66683C6.66659 7.8535 6.51992 8.00016 6.33325 8.00016ZM8.33325 10.6668H7.66659C7.47992 10.6668 7.33325 10.5202 7.33325 10.3335C7.33325 10.1468 7.47992 10.0002 7.66659 10.0002H8.33325C8.51992 10.0002 8.66659 10.1468 8.66659 10.3335C8.66659 10.5202 8.51992 10.6668 8.33325 10.6668ZM8.33325 9.3335H7.66659C7.47992 9.3335 7.33325 9.18683 7.33325 9.00016C7.33325 8.8135 7.47992 8.66683 7.66659 8.66683H8.33325C8.51992 8.66683 8.66659 8.8135 8.66659 9.00016C8.66659 9.18683 8.51992 9.3335 8.33325 9.3335ZM8.33325 8.00016H7.66659C7.47992 8.00016 7.33325 7.8535 7.33325 7.66683C7.33325 7.48016 7.47992 7.3335 7.66659 7.3335H8.33325C8.51992 7.3335 8.66659 7.48016 8.66659 7.66683C8.66659 7.8535 8.51992 8.00016 8.33325 8.00016ZM10.3333 10.6668H9.66659C9.47992 10.6668 9.33325 10.5202 9.33325 10.3335C9.33325 10.1468 9.47992 10.0002 9.66659 10.0002H10.3333C10.5199 10.0002 10.6666 10.1468 10.6666 10.3335C10.6666 10.5202 10.5199 10.6668 10.3333 10.6668ZM10.3333 9.3335H9.66659C9.47992 9.3335 9.33325 9.18683 9.33325 9.00016C9.33325 8.8135 9.47992 8.66683 9.66659 8.66683H10.3333C10.5199 8.66683 10.6666 8.8135 10.6666 9.00016C10.6666 9.18683 10.5199 9.3335 10.3333 9.3335ZM10.3333 8.00016H9.66659C9.47992 8.00016 9.33325 7.8535 9.33325 7.66683C9.33325 7.48016 9.47992 7.3335 9.66659 7.3335H10.3333C10.5199 7.3335 10.6666 7.48016 10.6666 7.66683C10.6666 7.8535 10.5199 8.00016 10.3333 8.00016Z'
    fill='#FFFFFF'
  />
);

export const AssetIcon = svgToIcon(
  <>
    <path
      d='M2.25 7.4715C2.25 7.1224 2.59869 6.88076 2.92556 7.00334L7.92556 8.87834C8.12071 8.95152 8.25 9.13808 8.25 9.3465V16C8.25 16.2761 8.02614 16.5 7.75 16.5H6.86803C6.79041 16.5 6.71385 16.4819 6.64443 16.4472L2.52639 14.3882C2.357 14.3035 2.25 14.1304 2.25 13.941V13.5V7.4715Z'
      fill='#4958A9'
    />
    <path
      d='M9.75 9.3465C9.75 9.13808 9.87929 8.95152 10.0744 8.87834L15.0744 7.00334C15.4013 6.88076 15.75 7.1224 15.75 7.4715V13.5V13.941C15.75 14.1304 15.643 14.3035 15.4736 14.3882L11.3556 16.4472C11.2861 16.4819 11.2096 16.5 11.132 16.5H10.25C9.97386 16.5 9.75 16.2761 9.75 16V9.3465Z'
      fill='#4958A9'
    />
    <path
      d='M8.16511 1.53184C8.22124 1.51078 8.28071 1.5 8.34067 1.5H9.65933C9.71929 1.5 9.77876 1.51078 9.83489 1.53184L15.4256 3.62834C15.6207 3.70152 15.75 3.88808 15.75 4.0965V4.9035C15.75 5.11192 15.6207 5.29848 15.4256 5.37166L9.83489 7.46816C9.77876 7.48922 9.71929 7.5 9.65933 7.5H8.34067C8.28071 7.5 8.22124 7.48922 8.16511 7.46816L2.57444 5.37166C2.37929 5.29848 2.25 5.11192 2.25 4.9035V4.0965C2.25 3.88808 2.37929 3.70152 2.57444 3.62834L8.16511 1.53184Z'
      fill='#4958A9'
    />
  </>
);

export const AssetIconGrey = svgToIcon(
  <>
    <path
      d='M2.25 7.4715C2.25 7.1224 2.59869 6.88076 2.92556 7.00334L7.92556 8.87834C8.12071 8.95152 8.25 9.13808 8.25 9.3465V16C8.25 16.2761 8.02614 16.5 7.75 16.5H6.86803C6.79041 16.5 6.71385 16.4819 6.64443 16.4472L2.52639 14.3882C2.357 14.3035 2.25 14.1304 2.25 13.941V13.5V7.4715Z'
      fill='#5D5D67'
    />
    <path
      d='M9.75 9.3465C9.75 9.13808 9.87929 8.95152 10.0744 8.87834L15.0744 7.00334C15.4013 6.88076 15.75 7.1224 15.75 7.4715V13.5V13.941C15.75 14.1304 15.643 14.3035 15.4736 14.3882L11.3556 16.4472C11.2861 16.4819 11.2096 16.5 11.132 16.5H10.25C9.97386 16.5 9.75 16.2761 9.75 16V9.3465Z'
      fill='#5D5D67'
    />
    <path
      d='M8.16511 1.53184C8.22124 1.51078 8.28071 1.5 8.34067 1.5H9.65933C9.71929 1.5 9.77876 1.51078 9.83489 1.53184L15.4256 3.62834C15.6207 3.70152 15.75 3.88808 15.75 4.0965V4.9035C15.75 5.11192 15.6207 5.29848 15.4256 5.37166L9.83489 7.46816C9.77876 7.48922 9.71929 7.5 9.65933 7.5H8.34067C8.28071 7.5 8.22124 7.48922 8.16511 7.46816L2.57444 5.37166C2.37929 5.29848 2.25 5.11192 2.25 4.9035V4.0965C2.25 3.88808 2.37929 3.70152 2.57444 3.62834L8.16511 1.53184Z'
      fill='#5D5D67'
    />
  </>
);

export const MatchedIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M14.3367 3.99618L7.74999 10.5828L5.42999 8.26951C5.30543 8.14468 5.13633 8.07452 4.95999 8.07452C4.78364 8.07452 4.61454 8.14468 4.48999 8.26951C4.22999 8.52951 4.22999 8.94951 4.48999 9.20951L7.27665 11.9962C7.53665 12.2562 7.95665 12.2562 8.21665 11.9962L15.2767 4.94284C15.5367 4.68284 15.5367 4.26284 15.2767 4.00284H15.27C15.0167 3.73618 14.5967 3.73618 14.3367 3.99618ZM11.51 4.00284C11.3854 3.87801 11.2163 3.80785 11.04 3.80785C10.8636 3.80785 10.6945 3.87801 10.57 4.00284L6.80999 7.76284L7.74999 8.70284L11.51 4.93618C11.7633 4.68284 11.7633 4.25618 11.51 4.00284ZM3.50999 12.0028L0.72332 9.21618C0.46332 8.95618 0.46332 8.52951 0.72332 8.26951C0.847875 8.14468 1.01698 8.07452 1.19332 8.07452C1.36967 8.07452 1.53877 8.14468 1.66332 8.26951L4.91665 11.5362L4.44999 12.0028C4.18999 12.2628 3.76999 12.2628 3.50999 12.0028Z'
    fill='#35A072'
  />
);

export const InProgressIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M4.00008 6.66675C3.26675 6.66675 2.66675 7.26675 2.66675 8.00008C2.66675 8.73341 3.26675 9.33341 4.00008 9.33341C4.73341 9.33341 5.33341 8.73341 5.33341 8.00008C5.33341 7.26675 4.73341 6.66675 4.00008 6.66675ZM12.0001 6.66675C11.2667 6.66675 10.6667 7.26675 10.6667 8.00008C10.6667 8.73341 11.2667 9.33341 12.0001 9.33341C12.7334 9.33341 13.3334 8.73341 13.3334 8.00008C13.3334 7.26675 12.7334 6.66675 12.0001 6.66675ZM6.66675 8.00008C6.66675 7.26675 7.26675 6.66675 8.00008 6.66675C8.73341 6.66675 9.33341 7.26675 9.33341 8.00008C9.33341 8.73341 8.73341 9.33341 8.00008 9.33341C7.26675 9.33341 6.66675 8.73341 6.66675 8.00008Z'
    fill='#C66FD4'
  />
);

export const PlusIcon = svgToIcon(
  <path
    d='M8.99992 5.66683H5.66659V9.00016C5.66659 9.36683 5.36659 9.66683 4.99992 9.66683C4.63325 9.66683 4.33325 9.36683 4.33325 9.00016V5.66683H0.999919C0.633252 5.66683 0.333252 5.36683 0.333252 5.00016C0.333252 4.6335 0.633252 4.3335 0.999919 4.3335H4.33325V1.00016C4.33325 0.633496 4.63325 0.333496 4.99992 0.333496C5.36659 0.333496 5.66659 0.633496 5.66659 1.00016V4.3335H8.99992C9.36659 4.3335 9.66659 4.6335 9.66659 5.00016C9.66659 5.36683 9.36659 5.66683 8.99992 5.66683Z'
    fill='#5D5D67'
  />,
  '0 0 10 10'
);

export const CanceledIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    fillOpacity='0.38'
    d='M8.33342 1.3335C4.65341 1.3335 1.66675 4.32016 1.66675 8.00016C1.66675 11.6802 4.65341 14.6668 8.33342 14.6668C12.0134 14.6668 15.0001 11.6802 15.0001 8.00016C15.0001 4.32016 12.0134 1.3335 8.33342 1.3335ZM3.00008 8.00016C3.00008 5.0535 5.38675 2.66683 8.33341 2.66683C9.56675 2.66683 10.7001 3.08683 11.6001 3.7935L4.12675 11.2668C3.42008 10.3668 3.00008 9.2335 3.00008 8.00016ZM5.06675 12.2068C5.96675 12.9135 7.10008 13.3335 8.33341 13.3335C11.2801 13.3335 13.6667 10.9468 13.6667 8.00016C13.6667 6.76683 13.2467 5.6335 12.5401 4.7335L5.06675 12.2068Z'
    fill='#1B1B1F'
  />
);

export const FailedIcon = svgToIcon(
  <path
    fillRule='evenodd'
    clipRule='evenodd'
    d='M12.5334 3.80664C12.4088 3.6818 12.2397 3.61165 12.0634 3.61165C11.887 3.61165 11.7179 3.6818 11.5934 3.80664L8.33335 7.05997L5.07335 3.79997C4.9488 3.67514 4.7797 3.60498 4.60335 3.60498C4.42701 3.60498 4.25791 3.67514 4.13335 3.79997C3.87335 4.05997 3.87335 4.47997 4.13335 4.73997L7.39335 7.99997L4.13335 11.26C3.87335 11.52 3.87335 11.94 4.13335 12.2C4.39335 12.46 4.81335 12.46 5.07335 12.2L8.33335 8.93997L11.5934 12.2C11.8534 12.46 12.2734 12.46 12.5334 12.2C12.7934 11.94 12.7934 11.52 12.5334 11.26L9.27335 7.99997L12.5334 4.73997C12.7867 4.48664 12.7867 4.05997 12.5334 3.80664Z'
    fill='#E96A63'
  />
);

export const SuccessfulIcon = svgToIcon(
  <path
    d='M6.20007 10.6031L3.86674 8.26976C3.7435 8.14499 3.57544 8.07477 3.40007 8.07477C3.2247 8.07477 3.05664 8.14499 2.9334 8.26976C2.6734 8.52976 2.6734 8.94309 2.9334 9.20309L5.72674 11.9964C5.98674 12.2564 6.40674 12.2564 6.66674 11.9964L13.7334 4.93643C13.9934 4.67643 13.9934 4.26309 13.7334 4.00309C13.6102 3.87833 13.4421 3.80811 13.2667 3.80811C13.0914 3.80811 12.9233 3.87833 12.8001 4.00309L6.20007 10.6031Z'
    fill='#35A072'
  />
);

export const CheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M9.26667 10.9231L11.9333 13.6154L21 5M19 13.6154V16.8462C19 18.0357 18.0449 19 16.8667 19L7.13333 19C5.95513 19 5 18.0357 5 16.8462V7.15385C5 5.96431 5.95513 5 7.13333 5H14" stroke="#3d4fb1" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const UnCheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M5 7V17C5 18.1046 5.89543 19 7 19H17C18.1046 19 19 18.1046 19 17V7C19 5.89543 18.1046 5 17 5H7C5.89543 5 5 5.89543 5 7Z" stroke="#3d4fb1" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);
