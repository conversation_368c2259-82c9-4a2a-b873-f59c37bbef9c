import { createTheme } from '@mui/material/styles';
import { grey, indigo } from '@mui/material/colors';

// Customized colors
const PRIMARY_LIGHT = '#4958A9';
const OUTLINED_BORDER = '#ABAAB5';
const BAR_PURPLE = '#C66FD4';
const BAR_ORANGE = '#E96A63';
const BAR_GREEN = '#35A072';
const BLACK = '#000000';
const ORANGE = '#F59352';
const HEADER_ICON = '#4958A9';
const GREY_ICON = '#DADADA';
const BACKGROUND_LIGHT = '#F8F8FB';
const GREY = '#5D5D67';
const WHITE = '#ffffff';
const ROW_HOVER = '#EEEFF9';
const INACTIVE_ICON = '#ABAAB5';
const DROPDOWN_ICON_BACKGROUND = '#DDE1FF';
const PRIMARY_CONTENT = '#DDE1FF';
const PRIMARY_CONTAINER = '#000E5E';
const MODAL_BACKGROUND = '#E1E1EC';

// Customized font.
const fontFamily = [
  'Roboto',
  '"Helvetica Neue"',
  'Arial',
  'sans-serif',
  '"Apple Color Emoji"',
  '"Segoe UI Emoji"',
  '"Segoe UI Symbol"',
].join(',');

const fontCommon = {
  fontFamily,
  fontWeight: 500,
  color: BLACK,
  textDecoration: 'none',
};

const SNACK_DURATION = 3000;
const SNACK_MAX = 3;

const snackBar = {
  styles: {
    fontSize: '14px',
    fontWeight: 700,
  },
  constants: {
    duration: SNACK_DURATION,
    max: SNACK_MAX,
    documentRoot: document.querySelector('body'),
  },
};

const htmlFontSize = 10;
const pxToRem = (size: number) => `${size / htmlFontSize}rem`;

// Custom theme.
const theme = createTheme({
  palette: {
    primary: {
      main: indigo[500],
      light: PRIMARY_LIGHT,
    },
    common: {
      orange: ORANGE,
      black: BLACK,
      white: WHITE,
      barPurple: BAR_PURPLE,
      barOrange: BAR_ORANGE,
      barGreen: BAR_GREEN,
      lightSurface: indigo[50],
      backgroundLight: BACKGROUND_LIGHT,
      headerIcon: HEADER_ICON,
      greyIcon: GREY,
      greyIconLight: GREY_ICON,
      rowHover: ROW_HOVER,
      inactiveIcon: INACTIVE_ICON,
      dropdownIconBackground: DROPDOWN_ICON_BACKGROUND,
      accordionBottom: OUTLINED_BORDER,
      icon: HEADER_ICON,
      primaryContent: PRIMARY_CONTENT,
      primaryContainer: PRIMARY_CONTAINER,
      modalBackground: MODAL_BACKGROUND,
      closeButton: GREY,
    },
  },
  components: {
    MuiTab: {
      styleOverrides: {
        root: {
          '&.MuiButtonBase-root': {
            fontSize: '14px',
          },
        },
      },
    },
    MuiSvgIcon: {
      variants: [
        {
          props: { fontSize: 'tag' },
          style: {
            fontSize: '12px',
          },
        },
        {
          props: { fontSize: 'buttonSmall' },
          style: {
            fontSize: '10px !important',
          },
        },
        {
          props: { fill: GREY_ICON },
          style: {
            // fontSize: '12px',
            color: GREY_ICON,
            fill: GREY_ICON,
          },
        },
      ],
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          '&$selected': {
            backgroundColor: 'red',
            '&:hover': {
              backgroundColor: 'orange',
            },
          },
        },
      },
    },
    MuiButton: {
      variants: [
        {
          props: { variant: 'grey' },
          style: {
            color: grey[700],
            fontSize: '14px',
          },
        },
        {
          props: { variant: 'cancel' },
          style: {
            color: GREY,
            fontSize: '14px',
          },
        },
        {
          props: { variant: 'apply' },
          style: {
            color: indigo[500],
            fontSize: '14px',
          },
        },
      ],
      styleOverrides: {
        root: {
          borderColor: OUTLINED_BORDER,
          borderRadius: 8,
          textTransform: 'unset',
          padding: '8px 24px',
          fontSize: '11px',
          height: 32,
          marginTop: 12,
          marginBottom: 12,
        },
        text: {
          textTransform: 'unset',
          padding: '8px 24px',
          fontSize: '11px',
          height: 32,
          marginTop: 12,
          marginBottom: 12,
        },
        contained: {
          borderRadius: 8,
          textTransform: 'unset',
          padding: '8px 24px',
          fontSize: '11px',
          height: 32,
          marginTop: 12,
          marginBottom: 12,
        },
      },
    },
    MuiSnackbar: {
      styleOverrides: {
        root: {
          zIndex: 3000,
        },
      },
    },
  },
  typography: {
    pxToRem,
    fontFamily,
    labelSmall: {
      ...fontCommon,
      color: GREY,
      fontSize: '11px',
      lineHeight: '16px',
      letterSpacing: '0.5px',
    },
    labelLarge: {
      ...fontCommon,
      fontSize: '14px',
      lineHeight: '20px',
      letterSpacing: '0.1px',
    },
    titleSmall: {
      ...fontCommon,
      fontSize: '14px',
      lineHeight: '20px',
      letterSpacing: '0.1px',
    },
    titleMedium: {
      ...fontCommon,
      fontSize: '16px',
      lineHeight: '24px',
      letterSpacing: '0.1px',
    },
    titleLarge: {
      ...fontCommon,
      height: '28px',
      fontSize: '24px',
      lineHeight: '22px',
    },
    headerlineSmall: {
      fontFamily,
      display: 'block',
      fontStyle: 'normal',
      fontWeight: 400,
      fontSize: '24px',
      lineHeight: '32px',
      marginBottom: '16px',
    },
    bodyMedium: {
      fontFamily,
      fontStyle: 'normal',
      fontWeight: 400,
      fontSize: '14px',
      lineHeight: '20px',
    },
    bodySmall: {
      fontFamily,
      fontStyle: 'normal',
      fontWeight: 400,
      fontSize: '12px',
    },
  },
});

export { theme, snackBar };
