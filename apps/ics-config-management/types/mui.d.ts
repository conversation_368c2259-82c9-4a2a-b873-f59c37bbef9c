import { Theme, ThemeOptions } from '@mui/material/styles';
import '@mui/material/styles/createPalette';
import { CSSProperties } from 'react';

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    titleSmall: true;
    titleMedium: true;
    labelSmall: true;
    titleLarge: true;
    headerlineSmall: true;
    bodyMedium: true;
    labelLarge: true;
    bodySmall: true;
  }
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    titleSmall: CSSProperties;
    titleMedium: CSSProperties;
    labelSmall: CSSProperties;
    titleLarge: CSSProperties;
    headerlineSmall: CSSProperties;
    bodyMedium: CSSProperties;
    labelLarge: CSSProperties;
    bodySmall: CSSProperties;
  }

  // Conflicting with M<PERSON> library, ignore it for now.
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface TypographyVariantsOptions extends Partial<TypographyVariants> {}

  type CustomTheme = Theme;
  type CustomThemeOptions = ThemeOptions;

  // Conflicting with M<PERSON> library, ignore it for now.
  // eslint-disable-next-line import/prefer-default-export
  export function createTheme(options?: CustomThemeOptions): CustomTheme;
}

declare module '@mui/material/styles/createPalette' {
  interface CommonColors {
    orange: string;
    black: string;
    white: string;
    lightSurface: string;
    barPurple: string;
    barOrange: string;
    barGreen: string;
    backgroundLight: string;
    headerIcon: string;
    greyIcon: string;
    greyIconLight: string;
    rowHover: string;
    inactiveIcon: string;
    dropdownIconBackground: string;
    accordionBottom: string;
    icon: string;
    primaryContent: string;
    primaryContainer: string;
    modalBackground: string;
    closeButton: string;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    grey: true;
    cancel: true;
    apply: true;
  }
}

declare module '@mui/material/SvgIcon' {
  interface SvgIconPropsSizeOverrides {
    tag: true;
    buttonSmall: true;
  }
}
